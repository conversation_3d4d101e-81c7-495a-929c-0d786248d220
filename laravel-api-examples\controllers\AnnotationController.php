<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Annotation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AnnotationController extends Controller
{
    /**
     * Display a listing of annotations.
     */
    public function index(): JsonResponse
    {
        $annotations = Annotation::orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'success' => true,
            'data' => $annotations
        ]);
    }

    /**
     * Store a newly created annotation.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'annotation_id' => 'required|string|unique:annotations,annotation_id',
            'annotation_data' => 'required|string',
        ]);

        $annotation = Annotation::create([
            'annotation_id' => $request->annotation_id,
            'annotation_data' => $request->annotation_data,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Annotation created successfully',
            'data' => $annotation
        ], 201);
    }

    /**
     * Display the specified annotation.
     */
    public function show(string $annotationId): JsonResponse
    {
        $annotation = Annotation::where('annotation_id', $annotationId)->first();

        if (!$annotation) {
            return response()->json([
                'success' => false,
                'message' => 'Annotation not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $annotation
        ]);
    }

    /**
     * Update the specified annotation.
     */
    public function update(Request $request, string $annotationId): JsonResponse
    {
        $annotation = Annotation::where('annotation_id', $annotationId)->first();

        if (!$annotation) {
            return response()->json([
                'success' => false,
                'message' => 'Annotation not found'
            ], 404);
        }

        $request->validate([
            'annotation_data' => 'required|string',
        ]);

        $annotation->update([
            'annotation_data' => $request->annotation_data,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Annotation updated successfully',
            'data' => $annotation
        ]);
    }

    /**
     * Remove the specified annotation.
     */
    public function destroy(string $annotationId): JsonResponse
    {
        $annotation = Annotation::where('annotation_id', $annotationId)->first();

        if (!$annotation) {
            return response()->json([
                'success' => false,
                'message' => 'Annotation not found'
            ], 404);
        }

        // Also delete related comments
        $annotation->comments()->delete();
        $annotation->delete();

        return response()->json([
            'success' => true,
            'message' => 'Annotation deleted successfully'
        ]);
    }

    /**
     * Remove all annotations.
     */
    public function destroyAll(): JsonResponse
    {
        Annotation::truncate();

        return response()->json([
            'success' => true,
            'message' => 'All annotations deleted successfully'
        ]);
    }
}
