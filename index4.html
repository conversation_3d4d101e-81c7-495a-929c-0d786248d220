<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Annotorious v3 with Comments - Bootstrap 5.3</title>

    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css"
        rel="stylesheet">

    <!-- Annotorious v3 CSS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/annotorious/3.0.0/annotorious.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/annotorious/3.0.0/annotorious.min.css">

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --hover-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        }

        body {
            background: var(--primary-gradient);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: white;
            border-radius: 1rem;
            box-shadow: var(--card-shadow);
            overflow: hidden;
            margin: 2rem 0;
        }

        .header-section {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header-section h1 {
            font-weight: 300;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-section p {
            opacity: 0.9;
            margin-bottom: 0;
        }

        .image-container {
            background: #f8f9fa;
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .image-wrapper {
            position: relative;
            background: white;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
        }

        .image-wrapper:hover {
            box-shadow: var(--hover-shadow);
        }

        .image-wrapper img {
            display: block;
            max-width: 100%;
            height: auto;
        }

        .sidebar {
            background: white;
            border-left: 1px solid #dee2e6;
            max-height: 70vh;
            overflow-y: auto;
        }

        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }

        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .stats-item:last-child {
            border-bottom: none;
        }

        .stats-value {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1rem;
        }

        .annotation-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .annotation-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }

        .annotation-item.selected {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
            box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.3);
        }

        .annotation-text {
            font-weight: 500;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .annotation-meta {
            font-size: 0.875rem;
            opacity: 0.7;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }

        .empty-state .display-1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .controls-section {
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 1.5rem;
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            color: white;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.4);
        }

        .btn-outline-gradient {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-outline-gradient:hover {
            background: var(--primary-gradient);
            border-color: transparent;
            color: white;
            transform: translateY(-2px);
        }

        .status-alert {
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }

        /* Custom Annotorious styles */
        .a9s-annotation {
            stroke: #667eea;
            stroke-width: 2;
            fill: rgba(102, 126, 234, 0.1);
        }

        .a9s-annotation.selected {
            stroke: #764ba2;
            stroke-width: 3;
            fill: rgba(118, 75, 162, 0.2);
        }

        .a9s-annotation:hover {
            stroke: #764ba2;
            stroke-width: 3;
            fill: rgba(118, 75, 162, 0.15);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-container {
                margin: 1rem;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .sidebar {
                border-left: none;
                border-top: 1px solid #dee2e6;
                max-height: 50vh;
            }
        }

        /* Scrollbar styling */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>

<body>
    <div class="container-fluid px-lg-4">
        <div class="main-container">
            <!-- Header Section -->
            <div class="header-section">
                <h1><i class="bi bi-pencil-square me-3"></i>Annotorious v3 with Comments</h1>
                <p class="lead">Create interactive annotations with comments using Bootstrap 5.3 design</p>
            </div>

            <!-- Alert for status messages -->
            <div class="px-4 pt-3">
                <div id="status-alert" class="alert alert-dismissible fade show status-alert d-none" role="alert">
                    <div class="d-flex align-items-center">
                        <i id="status-icon" class="bi me-2"></i>
                        <span id="status-message"></span>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>

            <!-- Main Content -->
            <div class="row g-0">
                <!-- Image Section -->
                <div class="col-lg-8">
                    <div class="image-container p-4">
                        <div class="image-wrapper">
                            <img id="sample-image"
                                src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                                alt="Sample landscape image for annotation" class="img-fluid">
                        </div>
                    </div>
                </div>

                <!-- Sidebar Section -->
                <div class="col-lg-4">
                    <div class="sidebar p-4">
                        <!-- Statistics Card -->
                        <div class="card stats-card mb-4">
                            <div class="card-body">
                                <h5 class="card-title d-flex align-items-center mb-3">
                                    <i class="bi bi-graph-up me-2"></i>
                                    Statistics
                                </h5>
                                <div class="stats-item">
                                    <span class="fw-medium">Total Annotations:</span>
                                    <span class="stats-value" id="total-count">0</span>
                                </div>
                                <div class="stats-item">
                                    <span class="fw-medium">With Comments:</span>
                                    <span class="stats-value" id="commented-count">0</span>
                                </div>
                                <div class="stats-item">
                                    <span class="fw-medium">Selected:</span>
                                    <span class="stats-value" id="selected-info">None</span>
                                </div>
                            </div>
                        </div>

                        <!-- Annotations List -->
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <h5 class="mb-0">
                                <i class="bi bi-chat-square-text me-2"></i>
                                Annotations
                            </h5>
                            <span class="badge bg-primary rounded-pill" id="annotations-badge">0</span>
                        </div>

                        <div id="annotations-list">
                            <div class="empty-state">
                                <div class="display-1">📝</div>
                                <h6 class="mb-2">No annotations yet</h6>
                                <p class="mb-0">Click and drag on the image to create your first annotation!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Controls Section -->
            <div class="controls-section">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-gradient" id="add-mode">
                                <i class="bi bi-plus-circle me-2"></i>Add Annotation
                            </button>
                            <button type="button" class="btn btn-outline-gradient" id="select-mode">
                                <i class="bi bi-cursor me-2"></i>Select
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-danger" id="clear-all">
                                <i class="bi bi-trash me-2"></i>Clear All
                            </button>
                            <button type="button" class="btn btn-outline-success" id="export-data">
                                <i class="bi bi-download me-2"></i>Export
                            </button>
                            <button type="button" class="btn btn-outline-info" id="load-sample">
                                <i class="bi bi-collection me-2"></i>Sample
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>

    <script>
        // Initialize Annotorious
        const anno = Annotorious.init({
            image: 'sample-image',
            widgets: [
                'COMMENT'
            ]
        });

        // Store annotations data
        let annotations = [];
        let selectedAnnotation = null;

        // DOM elements
        const statusAlert = document.getElementById('status-alert');
        const statusIcon = document.getElementById('status-icon');
        const statusMessage = document.getElementById('status-message');
        const annotationsListEl = document.getElementById('annotations-list');
        const totalCountEl = document.getElementById('total-count');
        const commentedCountEl = document.getElementById('commented-count');
        const selectedInfoEl = document.getElementById('selected-info');
        const annotationsBadge = document.getElementById('annotations-badge');

        // Event listeners for annotation creation
        anno.on('createAnnotation', (annotation) => {
            console.log('Annotation created:', annotation);
            annotations.push(annotation);
            updateUI();
            showStatus('Annotation created! Add a comment to describe what you see.', 'success');
        });

        // Event listeners for annotation updates
        anno.on('updateAnnotation', (annotation, previous) => {
            console.log('Annotation updated:', annotation);
            const index = annotations.findIndex(a => a.id === annotation.id);
            if (index !== -1) {
                annotations[index] = annotation;
            }
            updateUI();
            showStatus('Annotation updated successfully!', 'success');
        });

        // Event listeners for annotation deletion
        anno.on('deleteAnnotation', (annotation) => {
            console.log('Annotation deleted:', annotation);
            annotations = annotations.filter(a => a.id !== annotation.id);
            selectedAnnotation = null;
            updateUI();
            showStatus('Annotation deleted!', 'info');
        });

        // Event listeners for annotation selection
        anno.on('selectAnnotation', (annotation) => {
            console.log('Annotation selected:', annotation);
            selectedAnnotation = annotation;
            updateUI();
            highlightAnnotationInList(annotation.id);
        });

        // Event listeners for deselection
        anno.on('cancelSelected', () => {
            console.log('Selection cancelled');
            selectedAnnotation = null;
            updateUI();
            clearHighlightInList();
        });

        // Update UI function
        function updateUI() {
            updateStats();
            updateAnnotationsList();
        }

        // Update statistics
        function updateStats() {
            const total = annotations.length;
            const commented = annotations.filter(a =>
                a.bodies && a.bodies.some(b => b.type === 'TextualBody' && b.purpose === 'commenting')
            ).length;

            totalCountEl.textContent = total;
            commentedCountEl.textContent = commented;
            annotationsBadge.textContent = total;

            if (selectedAnnotation) {
                selectedInfoEl.textContent = `#${selectedAnnotation.id.substring(0, 8)}...`;
            } else {
                selectedInfoEl.textContent = 'None';
            }
        }

        // Update annotations list
        function updateAnnotationsList() {
            if (annotations.length === 0) {
                annotationsListEl.innerHTML = `
                    <div class="empty-state">
                        <div class="display-1">📝</div>
                        <h6 class="mb-2">No annotations yet</h6>
                        <p class="mb-0">Click and drag on the image to create your first annotation!</p>
                    </div>
                `;
                return;
            }

            annotationsListEl.innerHTML = annotations.map((annotation, index) => {
                const comment = annotation.bodies?.find(b => b.type === 'TextualBody' && b.purpose === 'commenting');
                const commentText = comment ? comment.value : 'No comment added';
                const isSelected = selectedAnnotation && selectedAnnotation.id === annotation.id;

                return `
                    <div class="annotation-item ${isSelected ? 'selected' : ''}" 
                         data-annotation-id="${annotation.id}"
                         onclick="selectAnnotation('${annotation.id}')">
                        <div class="annotation-text">${commentText}</div>
                        <div class="annotation-meta">
                            <i class="bi bi-hash"></i>
                            <span>Annotation ${index + 1}</span>
                            <i class="bi bi-clock"></i>
                            <span>${new Date().toLocaleTimeString()}</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Select annotation function
        function selectAnnotation(annotationId) {
            const annotation = annotations.find(a => a.id === annotationId);
            if (annotation) {
                anno.selectAnnotation(annotation);
            }
        }

        // Highlight annotation in list
        function highlightAnnotationInList(annotationId) {
            const items = document.querySelectorAll('.annotation-item');
            items.forEach(item => {
                if (item.dataset.annotationId === annotationId) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        // Clear highlight in list
        function clearHighlightInList() {
            const items = document.querySelectorAll('.annotation-item');
            items.forEach(item => item.classList.remove('selected'));
        }

        // Show status message using Bootstrap alert
        function showStatus(message, type = 'info') {
            const iconMap = {
                success: 'bi-check-circle-fill',
                info: 'bi-info-circle-fill',
                warning: 'bi-exclamation-triangle-fill',
                danger: 'bi-exclamation-circle-fill'
            };

            const alertClass = `alert-${type}`;

            statusAlert.className = `alert alert-dismissible fade show status-alert ${alertClass}`;
            statusIcon.className = `bi ${iconMap[type]} me-2`;
            statusMessage.textContent = message;
            statusAlert.classList.remove('d-none');

            // Auto-dismiss after 4 seconds
            setTimeout(() => {
                const alert = bootstrap.Alert.getOrCreateInstance(statusAlert);
                alert.close();
            }, 4000);
        }

        // Control buttons
        document.getElementById('add-mode').addEventListener('click', () => {
            anno.setDrawingTool('rect');
            showStatus('Draw mode activated. Click and drag to create annotations.', 'info');
        });

        document.getElementById('select-mode').addEventListener('click', () => {
            anno.setDrawingTool(null);
            showStatus('Select mode activated. Click on annotations to select them.', 'info');
        });

        document.getElementById('clear-all').addEventListener('click', () => {
            if (confirm('Are you sure you want to clear all annotations?')) {
                anno.clearAnnotations();
                annotations = [];
                selectedAnnotation = null;
                updateUI();
                showStatus('All annotations cleared!', 'warning');
            }
        });

        document.getElementById('export-data').addEventListener('click', () => {
            const data = {
                annotations: annotations,
                exported: new Date().toISOString(),
                total: annotations.length,
                metadata: {
                    version: '1.0',
                    created_with: 'Annotorious v3 + Bootstrap 5.3'
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `annotations-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            showStatus('Annotations exported successfully!', 'success');
        });

        document.getElementById('load-sample').addEventListener('click', () => {
            // Load sample annotations
            const sampleAnnotations = [
                {
                    id: 'sample-mountain-peaks',
                    type: 'Annotation',
                    body: [
                        {
                            type: 'TextualBody',
                            purpose: 'commenting',
                            value: 'Beautiful mountain landscape with snow-capped peaks dominating the horizon'
                        }
                    ],
                    target: {
                        source: 'sample-image',
                        selector: {
                            type: 'FragmentSelector',
                            conformsTo: 'http://www.w3.org/TR/media-frags/',
                            value: 'xywh=pixel:100,50,300,200'
                        }
                    }
                },
                {
                    id: 'sample-lake-reflection',
                    type: 'Annotation',
                    body: [
                        {
                            type: 'TextualBody',
                            purpose: 'commenting',
                            value: 'Serene lake with crystal clear water reflecting the surrounding mountains'
                        }
                    ],
                    target: {
                        source: 'sample-image',
                        selector: {
                            type: 'FragmentSelector',
                            conformsTo: 'http://www.w3.org/TR/media-frags/',
                            value: 'xywh=pixel:200,300,400,150'
                        }
                    }
                },
                {
                    id: 'sample-forest-area',
                    type: 'Annotation',
                    body: [
                        {
                            type: 'TextualBody',
                            purpose: 'commenting',
                            value: 'Dense forest area with various evergreen trees creating a natural border'
                        }
                    ],
                    target: {
                        source: 'sample-image',
                        selector: {
                            type: 'FragmentSelector',
                            conformsTo: 'http://www.w3.org/TR/media-frags/',
                            value: 'xywh=pixel:50,200,150,250'
                        }
                    }
                }
            ];

            // Clear existing annotations
            anno.clearAnnotations();
            annotations = [];

            // Add sample annotations
            sampleAnnotations.forEach(annotation => {
                anno.addAnnotation(annotation);
                annotations.push(annotation);
            });

            updateUI();
            showStatus('Sample annotations loaded successfully!', 'success');
        });

        // Initialize the UI
        updateUI();

        // Set initial drawing tool
        anno.setDrawingTool('rect');

        // Show welcome message
        setTimeout(() => {
            showStatus('Welcome! Click "Add Annotation" and drag on the image to get started.', 'info');
        }, 1000);

        console.log('Annotorious v3 with Bootstrap 5.3 initialized successfully!');
    </script>
</body>

</html>