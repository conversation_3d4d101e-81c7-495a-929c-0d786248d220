<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Annotorious v3 with Comments</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/annotorious/3.0.0/annotorious.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/annotorious/3.0.0/annotorious.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .content {
            display: flex;
            min-height: 600px;
        }

        .image-container {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
            position: relative;
        }

        .sidebar {
            width: 350px;
            background: white;
            border-left: 1px solid #e9ecef;
            padding: 30px;
            overflow-y: auto;
            max-height: 600px;
        }

        .sidebar h3 {
            color: #495057;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .annotation-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .annotation-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .annotation-item.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .annotation-text {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .annotation-meta {
            font-size: 0.85em;
            opacity: 0.7;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .status {
            padding: 10px 20px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            color: #155724;
            margin-bottom: 20px;
            display: none;
        }

        .image-wrapper {
            position: relative;
            display: inline-block;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .image-wrapper img {
            display: block;
            max-width: 100%;
            height: auto;
        }

        .stats {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .stats-item:last-child {
            margin-bottom: 0;
        }

        .stats-label {
            font-weight: 500;
            color: #495057;
        }

        .stats-value {
            color: #667eea;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3em;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* Custom annotation styles */
        .a9s-annotation {
            stroke: #667eea;
            stroke-width: 2;
            fill: rgba(102, 126, 234, 0.1);
        }

        .a9s-annotation.selected {
            stroke: #764ba2;
            stroke-width: 3;
            fill: rgba(118, 75, 162, 0.2);
        }

        .a9s-annotation:hover {
            stroke: #764ba2;
            stroke-width: 3;
            fill: rgba(118, 75, 162, 0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Annotorious v3 with Comments</h1>
            <p>Click and drag to create annotations, then add comments to describe what you see</p>
        </div>
        
        <div class="content">
            <div class="image-container">
                <div class="status" id="status"></div>
                <div class="image-wrapper">
                    <img id="sample-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg" alt="Sample image for annotation">
                </div>
            </div>
            
            <div class="sidebar">
                <div class="stats">
                    <div class="stats-item">
                        <span class="stats-label">Total Annotations:</span>
                        <span class="stats-value" id="total-count">0</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">With Comments:</span>
                        <span class="stats-value" id="commented-count">0</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">Selected:</span>
                        <span class="stats-value" id="selected-info">None</span>
                    </div>
                </div>
                
                <h3>Annotations</h3>
                <div id="annotations-list">
                    <div class="empty-state">
                        <div style="font-size: 2em; margin-bottom: 10px;">📝</div>
                        <p>No annotations yet. Click and drag on the image to create your first annotation!</p>
                    </div>
                </div>
            </div>
            
        </div>
        <div class="controls">
            <button class="btn btn-primary" id="add-mode">Add Annotation</button>
            <button class="btn btn-secondary" id="select-mode">Select Mode</button>
            <button class="btn btn-danger" id="clear-all">Clear All</button>
            <button class="btn btn-secondary" id="export-data">Export Data</button>
            <button class="btn btn-secondary" id="load-sample">Load Sample</button>
        </div>
    </div>

    <script>
        // Initialize Annotorious
        const anno = Annotorious.init({
            image: 'sample-image',
            widgets: [
                'COMMENT'
            ]
        });

        // Store annotations data
        let annotations = [];
        let selectedAnnotation = null;

        // DOM elements
        const statusEl = document.getElementById('status');
        const annotationsListEl = document.getElementById('annotations-list');
        const totalCountEl = document.getElementById('total-count');
        const commentedCountEl = document.getElementById('commented-count');
        const selectedInfoEl = document.getElementById('selected-info');

        // Event listeners for annotation creation
        anno.on('createAnnotation', (annotation) => {
            console.log('Annotation created:', annotation);
            annotations.push(annotation);
            updateUI();
            showStatus('Annotation created! Add a comment to describe what you see.', 'success');
        });

        // Event listeners for annotation updates
        anno.on('updateAnnotation', (annotation, previous) => {
            console.log('Annotation updated:', annotation);
            const index = annotations.findIndex(a => a.id === annotation.id);
            if (index !== -1) {
                annotations[index] = annotation;
            }
            updateUI();
            showStatus('Annotation updated successfully!', 'success');
        });

        // Event listeners for annotation deletion
        anno.on('deleteAnnotation', (annotation) => {
            console.log('Annotation deleted:', annotation);
            annotations = annotations.filter(a => a.id !== annotation.id);
            selectedAnnotation = null;
            updateUI();
            showStatus('Annotation deleted!', 'info');
        });

        // Event listeners for annotation selection
        anno.on('selectAnnotation', (annotation) => {
            console.log('Annotation selected:', annotation);
            selectedAnnotation = annotation;
            updateUI();
            highlightAnnotationInList(annotation.id);
        });

        // Event listeners for deselection
        anno.on('cancelSelected', () => {
            console.log('Selection cancelled');
            selectedAnnotation = null;
            updateUI();
            clearHighlightInList();
        });

        // Update UI function
        function updateUI() {
            updateStats();
            updateAnnotationsList();
        }

        // Update statistics
        function updateStats() {
            const total = annotations.length;
            const commented = annotations.filter(a => 
                a.bodies && a.bodies.some(b => b.type === 'TextualBody' && b.purpose === 'commenting')
            ).length;

            totalCountEl.textContent = total;
            commentedCountEl.textContent = commented;
            
            if (selectedAnnotation) {
                selectedInfoEl.textContent = `ID: ${selectedAnnotation.id.substring(0, 8)}...`;
            } else {
                selectedInfoEl.textContent = 'None';
            }
        }

        // Update annotations list
        function updateAnnotationsList() {
            if (annotations.length === 0) {
                annotationsListEl.innerHTML = `
                    <div class="empty-state">
                        <div style="font-size: 2em; margin-bottom: 10px;">📝</div>
                        <p>No annotations yet. Click and drag on the image to create your first annotation!</p>
                    </div>
                `;
                return;
            }

            annotationsListEl.innerHTML = annotations.map((annotation, index) => {
                const comment = annotation.bodies?.find(b => b.type === 'TextualBody' && b.purpose === 'commenting');
                const commentText = comment ? comment.value : 'No comment';
                const isSelected = selectedAnnotation && selectedAnnotation.id === annotation.id;
                
                return `
                    <div class="annotation-item ${isSelected ? 'selected' : ''}" 
                         data-annotation-id="${annotation.id}"
                         onclick="selectAnnotation('${annotation.id}')">
                        <div class="annotation-text">${commentText}</div>
                        <div class="annotation-meta">
                            Annotation #${index + 1} • ${new Date().toLocaleTimeString()}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Select annotation function
        function selectAnnotation(annotationId) {
            const annotation = annotations.find(a => a.id === annotationId);
            if (annotation) {
                anno.selectAnnotation(annotation);
            }
        }

        // Highlight annotation in list
        function highlightAnnotationInList(annotationId) {
            const items = document.querySelectorAll('.annotation-item');
            items.forEach(item => {
                if (item.dataset.annotationId === annotationId) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        // Clear highlight in list
        function clearHighlightInList() {
            const items = document.querySelectorAll('.annotation-item');
            items.forEach(item => item.classList.remove('selected'));
        }

        // Show status message
        function showStatus(message, type = 'info') {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }

        // Control buttons
        document.getElementById('add-mode').addEventListener('click', () => {
            anno.setDrawingTool('rect');
            showStatus('Draw mode activated. Click and drag to create annotations.', 'info');
        });

        document.getElementById('select-mode').addEventListener('click', () => {
            anno.setDrawingTool(null);
            showStatus('Select mode activated. Click on annotations to select them.', 'info');
        });

        document.getElementById('clear-all').addEventListener('click', () => {
            if (confirm('Are you sure you want to clear all annotations?')) {
                anno.clearAnnotations();
                annotations = [];
                selectedAnnotation = null;
                updateUI();
                showStatus('All annotations cleared!', 'info');
            }
        });

        document.getElementById('export-data').addEventListener('click', () => {
            const data = {
                annotations: annotations,
                exported: new Date().toISOString(),
                total: annotations.length
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'annotations-export.json';
            a.click();
            URL.revokeObjectURL(url);
            
            showStatus('Annotations exported successfully!', 'success');
        });

        document.getElementById('load-sample').addEventListener('click', () => {
            // Load sample annotations
            const sampleAnnotations = [
                {
                    id: 'sample-1',
                    type: 'Annotation',
                    body: [
                        {
                            type: 'TextualBody',
                            purpose: 'commenting',
                            value: 'Beautiful mountain landscape with snow-capped peaks'
                        }
                    ],
                    target: {
                        source: 'sample-image',
                        selector: {
                            type: 'FragmentSelector',
                            conformsTo: 'http://www.w3.org/TR/media-frags/',
                            value: 'xywh=pixel:100,50,300,200'
                        }
                    }
                },
                {
                    id: 'sample-2',
                    type: 'Annotation',
                    body: [
                        {
                            type: 'TextualBody',
                            purpose: 'commenting',
                            value: 'Serene lake reflecting the mountains'
                        }
                    ],
                    target: {
                        source: 'sample-image',
                        selector: {
                            type: 'FragmentSelector',
                            conformsTo: 'http://www.w3.org/TR/media-frags/',
                            value: 'xywh=pixel:200,300,400,150'
                        }
                    }
                }
            ];

            // Clear existing annotations
            anno.clearAnnotations();
            annotations = [];
            
            // Add sample annotations
            sampleAnnotations.forEach(annotation => {
                anno.addAnnotation(annotation);
                annotations.push(annotation);
            });
            
            updateUI();
            showStatus('Sample annotations loaded!', 'success');
        });

        // Initialize the UI
        updateUI();
        
        // Set initial drawing tool
        anno.setDrawingTool('rect');
        
        console.log('Annotorious v3 with Comments initialized successfully!');
    </script>
</body>
</html>