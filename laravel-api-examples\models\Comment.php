<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Comment extends Model
{
    use HasFactory;

    protected $fillable = [
        'text',
        'user',
        'position',
        'annotation_id',
    ];

    /**
     * Get the annotation that owns this comment.
     */
    public function annotation()
    {
        return $this->belongsTo(Annotation::class, 'annotation_id', 'annotation_id');
    }
}
