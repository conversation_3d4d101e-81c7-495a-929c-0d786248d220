<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AnnotationController;
use App\Http\Controllers\Api\CommentController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Annotation routes
Route::prefix('annotations')->group(function () {
    Route::get('/', [AnnotationController::class, 'index']);
    Route::post('/', [AnnotationController::class, 'store']);
    Route::get('/{annotationId}', [AnnotationController::class, 'show']);
    Route::put('/{annotationId}', [AnnotationController::class, 'update']);
    Route::delete('/{annotationId}', [AnnotationController::class, 'destroy']);
    Route::delete('/', [AnnotationController::class, 'destroyAll']);
});

// Comment routes
Route::prefix('comments')->group(function () {
    Route::get('/', [CommentController::class, 'index']);
    Route::post('/', [CommentController::class, 'store']);
    Route::get('/{comment}', [CommentController::class, 'show']);
    Route::put('/{comment}', [CommentController::class, 'update']);
    Route::delete('/{comment}', [CommentController::class, 'destroy']);
    Route::delete('/', [CommentController::class, 'destroyAll']);
    Route::get('/annotation/{annotationId}', [CommentController::class, 'getByAnnotation']);
});

// CORS preflight routes
Route::options('{any}', function () {
    return response('', 200)
        ->header('Access-Control-Allow-Origin', '*')
        ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
})->where('any', '.*');
