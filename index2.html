<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Annotorious v3 Example</title>
  <style>
    img {
      max-width: 100%;
    }
  </style>
</head>
<body>

  <h2>Annotate This Image (v3)</h2>
  <img id="annotatable" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg" />

  <script type="module">
    import { createImageAnnotator } from 'https://cdn.jsdelivr.net/npm/@annotorious/annotorious@3.2.1/+esm';

    const anno = createImageAnnotator({
      image: 'annotatable',
      widgets: [
        'COMMENT' // ✅ Enable comment support
      ]
    });

    // Optional: log annotations with comment
    anno.on('createAnnotation', annotation => {
      console.log('Created annotation:', annotation);
    });
  </script>

</body>
</html>
