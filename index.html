<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Annotorious User API Example</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/annotorious/3.0.9/annotorious.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .user-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .image-container {
            text-align: center;
            margin-bottom: 20px;
        }
        
        #sample-image {
            max-width: 100%;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        
        .button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #1976d2;
        }
        
        .button.danger {
            background: #f44336;
        }
        
        .button.danger:hover {
            background: #d32f2f;
        }
        
        input[type="text"] {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .annotations-list {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .annotation-item {
            background: white;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 3px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Annotorious User API Demo</h1>
        
        <div class="user-info">
            <strong>Current User:</strong> <span id="current-user">Not set</span>
        </div>
        
        <div class="controls">
            <input type="text" id="user-input" placeholder="Enter username" value="john_doe">
            <button class="button" onclick="setUser()">Set User</button>
            <button class="button" onclick="getCurrentUser()">Get Current User</button>
            <button class="button danger" onclick="clearUser()">Clear User</button>
            <button class="button" onclick="showUserAnnotations()">Show My Annotations</button>
        </div>
        
        <div class="image-container">
            <img id="sample-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg" alt="Sample image for annotation">
        </div>
        
        <div class="annotations-list">
            <h3>User Annotations:</h3>
            <div id="annotations-container">
                <p>No annotations yet. Click on the image to create some!</p>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/annotorious/3.0.9/annotorious.min.js"></script>
    <script>
        // Initialize Annotorious
        const anno = Annotorious.init({
            image: 'sample-image',
            locale: 'en'
        });
        
        // Set initial user
        anno.setUser({
            id: 'demo_user',
            displayName: 'Demo User'
        });
        
        // Update display
        updateCurrentUserDisplay();
        
        // Listen for annotation events
        anno.on('createAnnotation', function(annotation) {
            console.log('Annotation created:', annotation);
            updateAnnotationsList();
        });
        
        anno.on('updateAnnotation', function(annotation) {
            console.log('Annotation updated:', annotation);
            updateAnnotationsList();
        });
        
        anno.on('deleteAnnotation', function(annotation) {
            console.log('Annotation deleted:', annotation);
            updateAnnotationsList();
        });
        
        // User API Functions
        function setUser() {
            const username = document.getElementById('user-input').value.trim();
            if (username) {
                const user = {
                    id: username.toLowerCase().replace(/\s+/g, '_'),
                    displayName: username
                };
                
                // Set the user using Annotorious API
                anno.setUser(user);
                
                updateCurrentUserDisplay();
                updateAnnotationsList();
                
                console.log('User set:', user);
            } else {
                alert('Please enter a username');
            }
        }
        
        function getCurrentUser() {
            const currentUser = anno.getUser();
            console.log('Current user:', currentUser);
            
            if (currentUser) {
                alert(`Current user: ${currentUser.displayName} (ID: ${currentUser.id})`);
            } else {
                alert('No user is currently set');
            }
        }
        
        function clearUser() {
            anno.setUser(null);
            updateCurrentUserDisplay();
            updateAnnotationsList();
            console.log('User cleared');
        }
        
        function showUserAnnotations() {
            const currentUser = anno.getUser();
            if (!currentUser) {
                alert('No user is set');
                return;
            }
            
            const annotations = anno.getAnnotations();
            const userAnnotations = annotations.filter(annotation => {
                return annotation.target && annotation.target.creator && 
                       annotation.target.creator.id === currentUser.id;
            });
            
            console.log('User annotations:', userAnnotations);
            alert(`You have ${userAnnotations.length} annotations`);
        }
        
        function updateCurrentUserDisplay() {
            const currentUser = anno.getUser();
            const userDisplay = document.getElementById('current-user');
            
            if (currentUser) {
                userDisplay.textContent = `${currentUser.displayName} (${currentUser.id})`;
                userDisplay.style.color = '#4caf50';
            } else {
                userDisplay.textContent = 'Not set';
                userDisplay.style.color = '#666';
            }
        }
        
        function updateAnnotationsList() {
            const container = document.getElementById('annotations-container');
            const annotations = anno.getAnnotations();
            const currentUser = anno.getUser();
            
            if (annotations.length === 0) {
                container.innerHTML = '<p>No annotations yet. Click on the image to create some!</p>';
                return;
            }
            
            let html = '';
            annotations.forEach((annotation, index) => {
                const creator = annotation.target && annotation.target.creator;
                const isCurrentUser = currentUser && creator && creator.id === currentUser.id;
                
                html += `
                    <div class="annotation-item" style="border-left-color: ${isCurrentUser ? '#4caf50' : '#2196f3'}">
                        <strong>Annotation ${index + 1}</strong><br>
                        <small>Created by: ${creator ? creator.displayName : 'Unknown'}</small><br>
                        <small>Body: ${annotation.body && annotation.body.length > 0 ? annotation.body[0].value : 'No content'}</small>
                        ${isCurrentUser ? '<span style="color: #4caf50; font-weight: bold;"> (Your annotation)</span>' : ''}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Add some sample annotations for demonstration
        setTimeout(() => {
            // Add a sample annotation
            const sampleAnnotation = {
                "@context": "http://www.w3.org/ns/anno.jsonld",
                "type": "Annotation",
                "body": [{
                    "type": "TextualBody",
                    "value": "Sample annotation by demo user"
                }],
                "target": {
                    "source": "sample-image",
                    "selector": {
                        "type": "FragmentSelector",
                        "conformsTo": "http://www.w3.org/TR/media-frags/",
                        "value": "xywh=pixel:100,100,200,150"
                    }
                }
            };
            
            anno.addAnnotation(sampleAnnotation);
            updateAnnotationsList();
        }, 1000);
    </script>
</body>
</html>