<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Annotation extends Model
{
    use HasFactory;

    protected $fillable = [
        'annotation_id',
        'annotation_data',
    ];

    protected $casts = [
        'annotation_data' => 'array',
    ];

    /**
     * Get the comments for this annotation.
     */
    public function comments()
    {
        return $this->hasMany(Comment::class, 'annotation_id', 'annotation_id');
    }
}
