<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CommentController extends Controller
{
    /**
     * Display a listing of comments.
     */
    public function index(): JsonResponse
    {
        $comments = Comment::orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }

    /**
     * Store a newly created comment.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'text' => 'required|string',
            'user' => 'nullable|string',
            'position' => 'nullable|string',
            'annotation_id' => 'required|string',
        ]);

        $comment = Comment::create([
            'text' => $request->text,
            'user' => $request->user ?? 'Anonymous User',
            'position' => $request->position,
            'annotation_id' => $request->annotation_id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Comment created successfully',
            'data' => $comment
        ], 201);
    }

    /**
     * Display the specified comment.
     */
    public function show(Comment $comment): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $comment
        ]);
    }

    /**
     * Update the specified comment.
     */
    public function update(Request $request, Comment $comment): JsonResponse
    {
        $request->validate([
            'text' => 'required|string',
            'user' => 'nullable|string',
            'position' => 'nullable|string',
        ]);

        $comment->update($request->only(['text', 'user', 'position']));

        return response()->json([
            'success' => true,
            'message' => 'Comment updated successfully',
            'data' => $comment
        ]);
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(Comment $comment): JsonResponse
    {
        $comment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Comment deleted successfully'
        ]);
    }

    /**
     * Remove all comments.
     */
    public function destroyAll(): JsonResponse
    {
        Comment::truncate();

        return response()->json([
            'success' => true,
            'message' => 'All comments deleted successfully'
        ]);
    }

    /**
     * Get comments for a specific annotation.
     */
    public function getByAnnotation(string $annotationId): JsonResponse
    {
        $comments = Comment::where('annotation_id', $annotationId)
                          ->orderBy('created_at', 'desc')
                          ->get();

        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }
}
