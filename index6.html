<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.css">
    <script src="https://cdn.jsdelivr.net/npm/@annotorious/annotorious@latest/dist/annotorious.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        /* Essential custom styles only */
        .annotation-comment {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px 14px;
            border-radius: 8px;
            font-size: 0.875rem;
            max-width: 280px;
            word-wrap: break-word;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 2px solid #007bff;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .annotation-comment:hover {
            background: rgba(0, 0, 0, 0.95);
            border-color: #0056b3;
            transform: translateX(-50%) scale(1.02);
        }

        .annotation-comment::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid rgba(0, 0, 0, 0.9);
        }

        .comment-actions {
            display: none;
            position: absolute;
            top: -27px;
            right: -0px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 4px;
            padding: 2px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .annotation-comment:hover .comment-actions {
            display: flex;
            gap: 2px;
        }

        .comment-btn {
            background: none;
            border: none;
            padding: 4px 6px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: background-color 0.2s;
        }

        .comment-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .comment-btn.edit {
            color: #007bff;
        }

        .comment-btn.delete {
            color: #dc3545;
        }

        .comment-edit-area {
            display: none;
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border: 2px solid #007bff;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1001;
            min-width: 200px;
        }

        .comment-edit-input {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px;
            font-size: 0.875rem;
            resize: vertical;
            min-height: 60px;
            margin-bottom: 8px;
        }

        .comment-edit-buttons {
            display: flex;
            gap: 4px;
            justify-content: flex-end;
        }

        .comment-edit-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 0.75rem;
            cursor: pointer;
        }

        .comment-save-btn {
            background: #28a745;
            color: white;
        }

        .comment-cancel-btn {
            background: #6c757d;
            color: white;
        }

        .comment-input-box {
            position: absolute;
            background: white;
            border: 2px solid #007bff;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1001;
            min-width: 200px;
        }

        .comment-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 0.75rem;
            opacity: 0.8;
        }

        .comment-user {
            font-weight: bold;
            margin-right: 8px;
        }

        .comment-time {
            font-size: 0.7rem;
            opacity: 0.7;
        }

        .comment-text {
            font-size: 0.875rem;
            line-height: 1.3;
        }

        .image-container {
            position: relative;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <div class="row">
            <div class="col-md-8">
                <img id="my-image" src="https://images.pexels.com/photos/31241763/pexels-photo-31241763.jpeg"
                    alt="Annotatable image" class="img-fluid" />
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Instructions
                        </h5>
                    </div>
                    <div class="card-body p-3">
                        <div class="text-muted">
                            <h6 class="mb-3"><i class="fas fa-mouse-pointer me-2"></i>How to use:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-plus-circle text-success me-2"></i>Draw annotation → Comment box appears</li>
                                <li class="mb-2"><i class="fas fa-comment text-primary me-2"></i>Add comment or leave empty</li>
                                <li class="mb-2"><i class="fas fa-mouse-pointer text-warning me-2"></i>Click outside to save/cancel</li>
                                <li class="mb-2"><i class="fas fa-eye text-info me-2"></i><strong>Hover or select annotations</strong> to see comments</li>
                                <li class="mb-2"><i class="fas fa-edit text-success me-2"></i>Hover comments to edit/delete</li>
                            </ul>
                            <div class="alert alert-primary mt-3">
                                <i class="fas fa-mouse-pointer me-2"></i>
                                <strong>Smart Visibility:</strong> Comments appear when hovering or selecting annotations!
                            </div>
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-user me-2"></i>
                                <strong>User Info:</strong> Comments show user name and timestamp!
                            </div>
                            <div class="alert alert-warning mt-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Auto-save:</strong> Annotations without comments are automatically deleted!
                            </div>
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-keyboard me-2"></i>
                                <strong>Shortcuts:</strong> Ctrl+Enter to save, Escape to cancel
                            </div>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button class="btn btn-outline-danger btn-sm" onclick="clearAllComments()">
                                <i class="fas fa-trash-alt me-2"></i>Clear All Annotations
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        /*
        ========================================
        API INTEGRATION GUIDE
        ========================================

        This application currently uses localStorage for data persistence.
        To integrate with a backend API (Laravel/PHP), replace the localStorage
        functions with the corresponding API calls shown in the comments.

        REQUIRED API ENDPOINTS:

        1. COMMENTS API:
           - POST   /api/comments              (Create/Update comment)
           - GET    /api/comments/{id}         (Fetch single comment)
           - GET    /api/comments              (Fetch all comments)
           - DELETE /api/comments/{id}         (Delete comment)

        2. ANNOTATIONS API:
           - POST   /api/annotations           (Create/Update annotation)
           - GET    /api/annotations           (Fetch all annotations)
           - DELETE /api/annotations/{id}      (Delete annotation)
           - DELETE /api/annotations/clear-all (Clear all data)

        EXPECTED DATA STRUCTURE:

        Comment Object:
        {
            "id": "annotation-id",
            "annotation_id": "annotation-id",
            "text": "comment text",
            "user_name": "username",
            "timestamp": "2024-01-01 12:00:00",
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z"
        }

        Annotation Object:
        {
            "id": "annotation-id",
            "annotation_id": "annotation-id",
            "geometry": {
                "bounds": {
                    "minX": 100,
                    "minY": 100,
                    "maxX": 200,
                    "maxY": 200
                }
            },
            "annotation_data": { "full": "annotorious annotation object" },
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z"
        }

        CSRF TOKEN:
        Make sure to include CSRF token in your HTML head:
        <meta name="csrf-token" content="{{ csrf_token() }}">

        ========================================
        */

        // Comment management functions - separate from annotations
        function saveCommentToStorage(annotationId, commentText, userName = 'DemoUser') {
            // TODO: REPLACE WITH API CALL - CREATE/UPDATE COMMENT
            /*
            DEMO API CALL FOR SAVING COMMENT:

            fetch('/api/comments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    annotation_id: annotationId,
                    text: commentText,
                    user_name: userName
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Comment saved to database:', data);
                // Update local storage as backup
                let comments = JSON.parse(localStorage.getItem('annotation-comments') || '{}');
                comments[annotationId] = data;
                localStorage.setItem('annotation-comments', JSON.stringify(comments));
            })
            .catch(error => {
                console.error('Error saving comment:', error);
                // Fallback to localStorage on error
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let comments = JSON.parse(localStorage.getItem('annotation-comments') || '{}');
            comments[annotationId] = {
                text: commentText,
                timestamp: new Date().toLocaleString(),
                user: userName,
                id: annotationId
            };
            localStorage.setItem('annotation-comments', JSON.stringify(comments));
            console.log('Saved comment:', comments[annotationId]);
        }

        function getCommentFromStorage(annotationId) {
            // TODO: REPLACE WITH API CALL - FETCH COMMENT
            /*
            DEMO API CALL FOR FETCHING COMMENT:

            fetch(`/api/comments/${annotationId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Comment fetched from database:', data);
                return data;
            })
            .catch(error => {
                console.error('Error fetching comment:', error);
                // Fallback to localStorage on error
                let comments = JSON.parse(localStorage.getItem('annotation-comments') || '{}');
                return comments[annotationId] || null;
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let comments = JSON.parse(localStorage.getItem('annotation-comments') || '{}');
            return comments[annotationId] || null;
        }

        function removeCommentFromStorage(annotationId) {
            // TODO: REPLACE WITH API CALL - DELETE COMMENT
            /*
            DEMO API CALL FOR DELETING COMMENT:

            fetch(`/api/comments/${annotationId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Comment deleted from database:', data);
                // Remove from local storage as well
                let comments = JSON.parse(localStorage.getItem('annotation-comments') || '{}');
                delete comments[annotationId];
                localStorage.setItem('annotation-comments', JSON.stringify(comments));
            })
            .catch(error => {
                console.error('Error deleting comment:', error);
                // Fallback to localStorage on error
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let comments = JSON.parse(localStorage.getItem('annotation-comments') || '{}');
            delete comments[annotationId];
            localStorage.setItem('annotation-comments', JSON.stringify(comments));
        }

        function getAllCommentsFromStorage() {
            // TODO: REPLACE WITH API CALL - FETCH ALL COMMENTS
            /*
            DEMO API CALL FOR FETCHING ALL COMMENTS:

            fetch('/api/comments')
            .then(response => response.json())
            .then(data => {
                console.log('All comments fetched from database:', data);
                // Update local storage as backup
                localStorage.setItem('annotation-comments', JSON.stringify(data));
                return data;
            })
            .catch(error => {
                console.error('Error fetching all comments:', error);
                // Fallback to localStorage on error
                return JSON.parse(localStorage.getItem('annotation-comments') || '{}');
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            return JSON.parse(localStorage.getItem('annotation-comments') || '{}');
        }

        // Helper function to format time ago
        function getTimeAgo(timestamp) {
            if (!timestamp) return 'Unknown time';

            const now = new Date();
            const commentTime = new Date(timestamp);
            const diffInSeconds = Math.floor((now - commentTime) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;

            return commentTime.toLocaleDateString();
        }

        function saveAnnotationToStorage(annotation) {
            // TODO: REPLACE WITH API CALL - CREATE/UPDATE ANNOTATION
            /*
            DEMO API CALL FOR SAVING ANNOTATION:

            const cleanAnnotation = {
                ...annotation,
                bodies: annotation.bodies.filter(b => b.purpose !== 'commenting')
            };

            fetch('/api/annotations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    annotation_id: annotation.id,
                    geometry: cleanAnnotation.target.selector.geometry,
                    annotation_data: cleanAnnotation
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Annotation saved to database:', data);
                // Update local storage as backup
                let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
                annotations = annotations.filter(a => a.id !== annotation.id);
                annotations.push(cleanAnnotation);
                localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
            })
            .catch(error => {
                console.error('Error saving annotation:', error);
                // Fallback to localStorage on error
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let annotations = JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            // Remove existing annotation with same ID if it exists
            annotations = annotations.filter(a => a.id !== annotation.id);
            // Store annotation without comment data
            const cleanAnnotation = {
                ...annotation,
                bodies: annotation.bodies.filter(b => b.purpose !== 'commenting')
            };
            annotations.push(cleanAnnotation);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }

        function loadAnnotationsFromStorage() {
            // TODO: REPLACE WITH API CALL - FETCH ALL ANNOTATIONS
            /*
            DEMO API CALL FOR LOADING ALL ANNOTATIONS:

            fetch('/api/annotations')
            .then(response => response.json())
            .then(data => {
                console.log('All annotations fetched from database:', data);
                // Update local storage as backup
                localStorage.setItem('annotorious-annotations', JSON.stringify(data));
                return data;
            })
            .catch(error => {
                console.error('Error fetching annotations:', error);
                // Fallback to localStorage on error
                return JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            return JSON.parse(localStorage.getItem('annotorious-annotations') || '[]');
        }

        function removeAnnotationFromStorage(annotationId) {
            // TODO: REPLACE WITH API CALL - DELETE ANNOTATION
            /*
            DEMO API CALL FOR DELETING ANNOTATION:

            fetch(`/api/annotations/${annotationId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Annotation deleted from database:', data);
                // Remove from local storage as well
                let annotations = loadAnnotationsFromStorage();
                annotations = annotations.filter(a => a.id !== annotationId);
                localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
            })
            .catch(error => {
                console.error('Error deleting annotation:', error);
                // Fallback to localStorage on error
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            let annotations = loadAnnotationsFromStorage();
            annotations = annotations.filter(a => a.id !== annotationId);
            localStorage.setItem('annotorious-annotations', JSON.stringify(annotations));
        }





        // Function to highlight annotation on the image
        function highlightAnnotationOnImage(annotationId) {
            if (!window.annotoriusInstance) return;

            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                // First, clear any existing selections
                window.annotoriusInstance.clearSelection();

                // Select the annotation to highlight it
                window.annotoriusInstance.setSelected(annotation);

                // Add custom styling for enhanced visibility
                window.annotoriusInstance.setStyle((ann, state) => {
                    if (ann.id === annotationId && state.selected) {
                        return {
                            fill: '#ff6b6b',
                            fillOpacity: 0.3,
                            stroke: '#ff6b6b',
                            strokeWidth: 3,
                            strokeOpacity: 1
                        };
                    }
                    // Default style for other annotations
                    return {
                        fill: '#007bff',
                        fillOpacity: 0.2,
                        stroke: '#007bff',
                        strokeWidth: 2,
                        strokeOpacity: 0.8
                    };
                });

                console.log('Annotation highlighted on image:', annotationId);
            }
        }





        // Function to add persistent comment display under annotation
        function addAnnotationComment(annotation) {
            // Get comment from separate storage
            const commentData = getCommentFromStorage(annotation.id);
            const commentText = commentData ? commentData.text : '';

            console.log('Adding comment for annotation:', annotation.id, 'Text:', commentText);

            // Create a comment element
            const commentId = `comment-${annotation.id}`;

            // Remove existing comment if any
            const existingComment = document.getElementById(commentId);
            if (existingComment) {
                existingComment.remove();
            }

            // Get annotation bounds and image element
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');

            // Get image dimensions and position
            const imageRect = imageElement.getBoundingClientRect();
            const imageContainer = imageElement.parentElement;

            // Calculate position relative to the container
            const scaleX = imageRect.width / imageElement.naturalWidth;
            const scaleY = imageRect.height / imageElement.naturalHeight;

            const commentX = (bounds.minX + (bounds.maxX - bounds.minX) / 2) * scaleX;
            const commentY = bounds.maxY * scaleY + 10; // 10px below the annotation

            // Create comment element
            const commentElement = document.createElement('div');
            commentElement.id = commentId;
            commentElement.className = 'annotation-comment';
            commentElement.style.cssText = `
                left: ${commentX}px;
                top: ${commentY}px;
                transform: translateX(-50%);
                display: none;
            `;

            // Get user info and format time
            const userName = commentData ? commentData.user || 'DemoUser' : 'DemoUser';
            const timeAgo = commentData ? getTimeAgo(commentData.timestamp) : 'Just now';

            // Create comment content structure
            commentElement.innerHTML = `
                <div class="comment-header">
                    <span class="comment-user">${userName}</span>
                    <span class="comment-time">${timeAgo}</span>
                </div>
                <div class="comment-text bg-white text-dark p-1 rounded-1">${commentText.trim() || 'No comment'}</div>
                <div class="comment-actions">
                    <button class="comment-btn edit" onclick="editAnnotationComment('${annotation.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="comment-btn delete" onclick="deleteAnnotationComment('${annotation.id}')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="comment-edit-area" id="edit-area-${annotation.id}">
                    <textarea class="comment-edit-input" id="edit-input-${annotation.id}" placeholder="Edit comment..."
                              onkeydown="handleEditKeydown(event, '${annotation.id}')">${commentText}</textarea>
                    <div class="comment-edit-buttons">
                        <button class="comment-edit-btn comment-save-btn" onclick="saveAnnotationComment('${annotation.id}')">Save</button>
                        <button class="comment-edit-btn comment-cancel-btn" onclick="cancelEditAnnotationComment('${annotation.id}')">Cancel</button>
                    </div>
                </div>
            `;

            // Add hover event listeners to keep comment visible when hovering over it
            commentElement.addEventListener('mouseenter', function() {
                commentElement.style.display = 'block';
            });

            commentElement.addEventListener('mouseleave', function() {
                // Check if annotation is selected
                const isSelected = window.selectedAnnotations &&
                                 window.selectedAnnotations.some(selected => selected.id === annotation.id);

                // Only hide if not editing and not selected
                const editArea = document.getElementById(`edit-area-${annotation.id}`);
                if ((!editArea || editArea.style.display === 'none') && !isSelected) {
                    commentElement.style.display = 'none';
                }
            });

            // Add to image container
            imageContainer.classList.add('image-container');
            imageContainer.appendChild(commentElement);
        }

        // Function to show comment input box for new annotation
        function showCommentInputBox(annotation) {
            const bounds = annotation.target.selector.geometry.bounds;
            const imageElement = document.getElementById('my-image');
            const imageContainer = imageElement.parentElement;

            // Calculate position
            const scaleX = imageElement.getBoundingClientRect().width / imageElement.naturalWidth;
            const scaleY = imageElement.getBoundingClientRect().height / imageElement.naturalHeight;

            const commentX = (bounds.minX + (bounds.maxX - bounds.minX) / 2) * scaleX;
            const commentY = bounds.maxY * scaleY + 10;

            // Create comment input box
            const inputBoxId = `comment-input-${annotation.id}`;
            const inputBox = document.createElement('div');
            inputBox.id = inputBoxId;
            inputBox.className = 'comment-input-box';
            inputBox.style.cssText = `
                position: absolute;
                left: ${commentX}px;
                top: ${commentY}px;
                transform: translateX(-50%);
                background: white;
                border: 2px solid #007bff;
                border-radius: 6px;
                padding: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 1001;
                min-width: 200px;
            `;

            inputBox.innerHTML = `
                <textarea class="comment-edit-input" id="new-comment-${annotation.id}" placeholder="Add a comment..."
                          onkeydown="handleNewCommentKeydown(event, '${annotation.id}')" autofocus></textarea>
                <div class="comment-edit-buttons">
                    <button class="comment-edit-btn comment-save-btn" onclick="saveNewComment('${annotation.id}')">Save</button>
                    <button class="comment-edit-btn comment-cancel-btn" onclick="cancelNewComment('${annotation.id}')">Cancel</button>
                </div>
            `;

            imageContainer.appendChild(inputBox);

            // Focus the textarea
            setTimeout(() => {
                const textarea = document.getElementById(`new-comment-${annotation.id}`);
                if (textarea) {
                    textarea.focus();
                }
            }, 100);

            // Store reference to current editing annotation
            window.currentEditingAnnotation = annotation;
        }

        // Function to remove annotation comment
        function removeAnnotationComment(annotationId) {
            const commentId = `comment-${annotationId}`;
            const commentElement = document.getElementById(commentId);
            if (commentElement) {
                commentElement.remove();
            }
        }

        // Function to show comment
        function showComment(annotationId) {
            const commentElement = document.getElementById(`comment-${annotationId}`);
            if (commentElement) {
                commentElement.style.display = 'block';
            }
        }

        // Function to hide comment
        function hideComment(annotationId) {
            const commentElement = document.getElementById(`comment-${annotationId}`);
            if (commentElement) {
                commentElement.style.display = 'none';
            }
        }

        // Function to hide all comments
        function hideAllComments() {
            const commentElements = document.querySelectorAll('.annotation-comment');
            commentElements.forEach(element => {
                element.style.display = 'none';
            });
        }

        // Function to edit annotation comment
        function editAnnotationComment(annotationId) {
            const commentElement = document.getElementById(`comment-${annotationId}`);
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const editInput = document.getElementById(`edit-input-${annotationId}`);

            if (commentElement && editArea && editInput) {
                // Ensure comment is visible during editing
                commentElement.style.display = 'block';

                // Get current comment from storage
                const commentData = getCommentFromStorage(annotationId);
                const currentText = commentData ? commentData.text : '';
                editInput.value = currentText;

                editArea.style.display = 'block';
                editInput.focus();
                editInput.select();
            }
        }

        // Function to cancel editing annotation comment
        function cancelEditAnnotationComment(annotationId) {
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const editInput = document.getElementById(`edit-input-${annotationId}`);

            if (editArea && editInput) {
                editArea.style.display = 'none';
                // Reset input to original value from storage
                const commentData = getCommentFromStorage(annotationId);
                const originalText = commentData ? commentData.text : '';
                editInput.value = originalText;
            }
        }

        // Function to save annotation comment
        function saveAnnotationComment(annotationId) {
            const editInput = document.getElementById(`edit-input-${annotationId}`);
            const editArea = document.getElementById(`edit-area-${annotationId}`);
            const commentTextElement = document.querySelector(`#comment-${annotationId} .comment-text`);

            if (!editInput || !editArea || !commentTextElement) return;

            const newText = editInput.value.trim();

            // Get existing comment data to preserve user info
            const existingComment = getCommentFromStorage(annotationId);
            const userName = existingComment ? existingComment.user : 'DemoUser';

            // Save comment to separate storage with preserved user info
            saveCommentToStorage(annotationId, newText, userName);

            // Update the display
            commentTextElement.textContent = newText || 'No comment';
            editArea.style.display = 'none';

            // Refresh the comment display to show updated time
            const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
            if (annotation) {
                addAnnotationComment(annotation);
            }
        }

        // Function to delete annotation comment (and annotation)
        function deleteAnnotationComment(annotationId) {
            if (confirm('Are you sure you want to delete this annotation and its comment?')) {
                // Remove from Annotorious
                if (window.annotoriusInstance) {
                    const annotation = window.annotoriusInstance.getAnnotations().find(a => a.id === annotationId);
                    if (annotation) {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    }
                }

                // Remove from localStorage
                removeAnnotationFromStorage(annotationId);
                removeCommentFromStorage(annotationId);

                // Remove the comment display
                removeAnnotationComment(annotationId);
            }
        }

        // Function to save new comment
        function saveNewComment(annotationId) {
            const textarea = document.getElementById(`new-comment-${annotationId}`);
            const inputBox = document.getElementById(`comment-input-${annotationId}`);

            if (textarea && inputBox) {
                const commentText = textarea.value.trim();

                if (commentText) {
                    // Get current user name (you can modify this to get from your user system)
                    const currentUser = window.annotoriusInstance?.getUser?.()?.name || 'DemoUser';

                    // Save comment to storage with user info
                    saveCommentToStorage(annotationId, commentText, currentUser);

                    // Save annotation to storage
                    if (window.currentEditingAnnotation) {
                        saveAnnotationToStorage(window.currentEditingAnnotation);
                    }

                    // Remove input box
                    inputBox.remove();

                    // Add comment display
                    if (window.currentEditingAnnotation) {
                        addAnnotationComment(window.currentEditingAnnotation);
                    }
                } else {
                    // No comment text, delete the annotation
                    cancelNewComment(annotationId);
                }

                window.currentEditingAnnotation = null;
            }
        }

        // Function to cancel new comment (and delete annotation)
        function cancelNewComment(annotationId) {
            const inputBox = document.getElementById(`comment-input-${annotationId}`);

            if (inputBox) {
                inputBox.remove();
            }

            // Delete the annotation since no comment was added
            if (window.annotoriusInstance && window.currentEditingAnnotation) {
                window.annotoriusInstance.removeAnnotation(window.currentEditingAnnotation);
            }

            window.currentEditingAnnotation = null;
        }

        // Function to handle keyboard events for new comment
        function handleNewCommentKeydown(event, annotationId) {
            if (event.key === 'Enter' && event.ctrlKey) {
                // Ctrl+Enter to save
                event.preventDefault();
                saveNewComment(annotationId);
            } else if (event.key === 'Escape') {
                // Escape to cancel
                event.preventDefault();
                cancelNewComment(annotationId);
            }
        }

        // Function to handle keyboard events in edit mode
        function handleEditKeydown(event, annotationId) {
            if (event.key === 'Enter' && event.ctrlKey) {
                // Ctrl+Enter to save
                event.preventDefault();
                saveAnnotationComment(annotationId);
            } else if (event.key === 'Escape') {
                // Escape to cancel
                event.preventDefault();
                cancelEditAnnotationComment(annotationId);
            }
        }

        function clearAllComments() {
            if (confirm('Are you sure you want to clear all annotations? This action cannot be undone.')) {
                // TODO: REPLACE WITH API CALL - DELETE ALL ANNOTATIONS AND COMMENTS
                /*
                DEMO API CALL FOR CLEARING ALL DATA:

                fetch('/api/annotations/clear-all', {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('All annotations and comments cleared from database:', data);
                    // Clear local storage as well
                    localStorage.removeItem('annotation-comments');
                    localStorage.removeItem('annotorious-annotations');

                    // Clear all annotations from the map
                    if (window.annotoriusInstance) {
                        const annotations = window.annotoriusInstance.getAnnotations();
                        annotations.forEach(annotation => {
                            window.annotoriusInstance.removeAnnotation(annotation);
                        });
                    }

                    // Remove all comment elements from the page
                    const commentElements = document.querySelectorAll('.annotation-comment, .comment-input-box');
                    commentElements.forEach(element => {
                        element.remove();
                    });
                })
                .catch(error => {
                    console.error('Error clearing all data:', error);
                    // Fallback to localStorage on error
                });
                */

                // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
                localStorage.removeItem('annotation-comments');
                localStorage.removeItem('annotorious-annotations');

                // Clear all annotations from the map
                if (window.annotoriusInstance) {
                    const annotations = window.annotoriusInstance.getAnnotations();
                    annotations.forEach(annotation => {
                        window.annotoriusInstance.removeAnnotation(annotation);
                    });
                }

                // Remove all comment elements from the page
                const commentElements = document.querySelectorAll('.annotation-comment, .comment-input-box');
                commentElements.forEach(element => {
                    element.remove();
                });
            }
        }



        function loadExistingAnnotations() {
            if (!window.annotoriusInstance) return;

            // TODO: REPLACE WITH API CALL - LOAD ANNOTATIONS AND COMMENTS ON PAGE LOAD
            /*
            DEMO API CALL FOR LOADING EXISTING DATA:

            Promise.all([
                fetch('/api/annotations').then(response => response.json()),
                fetch('/api/comments').then(response => response.json())
            ])
            .then(([annotationsData, commentsData]) => {
                console.log('Loaded annotations from database:', annotationsData);
                console.log('Loaded comments from database:', commentsData);

                // Update local storage as backup
                localStorage.setItem('annotorious-annotations', JSON.stringify(annotationsData));
                localStorage.setItem('annotation-comments', JSON.stringify(commentsData));

                // Load annotations into the map
                annotationsData.forEach(annotation => {
                    try {
                        window.annotoriusInstance.addAnnotation(annotation);
                        setTimeout(() => {
                            addAnnotationComment(annotation);
                        }, 500);
                        console.log('Restored annotation:', annotation.id);
                    } catch (error) {
                        console.error('Error restoring annotation:', error, annotation);
                    }
                });
            })
            .catch(error => {
                console.error('Error loading data from API:', error);
                // Fallback to localStorage on error
                const savedAnnotations = loadAnnotationsFromStorage();
                savedAnnotations.forEach(annotation => {
                    try {
                        window.annotoriusInstance.addAnnotation(annotation);
                        setTimeout(() => {
                            addAnnotationComment(annotation);
                        }, 500);
                        console.log('Restored annotation from localStorage:', annotation.id);
                    } catch (error) {
                        console.error('Error restoring annotation:', error, annotation);
                    }
                });
            });
            */

            // CURRENT LOCALSTORAGE IMPLEMENTATION (REPLACE WITH API CALL ABOVE)
            const savedAnnotations = loadAnnotationsFromStorage();
            console.log('Loading saved annotations:', savedAnnotations);

            savedAnnotations.forEach(annotation => {
                try {
                    // Add the annotation to the map
                    window.annotoriusInstance.addAnnotation(annotation);

                    // Add comment display for restored annotation
                    setTimeout(() => {
                        addAnnotationComment(annotation);
                    }, 500); // Small delay to ensure annotation is rendered

                    console.log('Restored annotation:', annotation.id);
                } catch (error) {
                    console.error('Error restoring annotation:', error, annotation);
                }
            });
        }

        // Add click outside detection
        document.addEventListener('click', function(event) {
            if (window.currentEditingAnnotation) {
                const inputBox = document.getElementById(`comment-input-${window.currentEditingAnnotation.id}`);
                if (inputBox && !inputBox.contains(event.target)) {
                    // Check if there's text in the input
                    const textarea = document.getElementById(`new-comment-${window.currentEditingAnnotation.id}`);
                    if (textarea) {
                        const commentText = textarea.value.trim();
                        if (commentText) {
                            // Save the comment
                            saveNewComment(window.currentEditingAnnotation.id);
                        } else {
                            // Cancel and delete annotation
                            cancelNewComment(window.currentEditingAnnotation.id);
                        }
                    }
                }
            }
        });

        // jQuery document ready function
        $(document).ready(function () {
            // Initialize global variables
            window.selectedAnnotations = [];

            const anno = Annotorious.createImageAnnotator('my-image', {
                widgets: [
                    // Enables the default COMMENT input box but we'll override the behavior
                    { widget: 'COMMENT' }
                ],
                drawingEnabled: true
            });

            // Store the annotorious instance globally for access from other functions
            window.annotoriusInstance = anno;

            anno.setUser({
                id: 'user-123',
                name: 'sourabh'
            });

            // Load existing annotations from localStorage after a short delay
            setTimeout(() => {
                loadExistingAnnotations();
            }, 100);

            // Event: When annotation is created
            anno.on('createAnnotation', annotation => {
                console.log('Created annotation:', annotation);

                // Show comment input box for new annotation
                showCommentInputBox(annotation);
            });

            // Event: When annotation is updated
            anno.on('updateAnnotation', (annotation, previous) => {
                console.log('Annotation updated:', annotation, previous);

                // Update the annotation in localStorage
                saveAnnotationToStorage(annotation);

                // Update the comment display
                addAnnotationComment(annotation);
            });

            // Event: When annotation is deleted
            anno.on('deleteAnnotation', annotation => {
                console.log('Annotation deleted:', annotation);

                // Remove annotation from localStorage
                removeAnnotationFromStorage(annotation.id);
                removeCommentFromStorage(annotation.id);

                // Remove the comment display
                removeAnnotationComment(annotation.id);
            });

            // Event: When annotation selection changes
            anno.on('selectionChanged', annotations => {
                console.log('Selection changed:', annotations);

                // Store currently selected annotations
                window.selectedAnnotations = annotations || [];

                // Hide all comments first
                hideAllComments();

                // Show comments for selected annotations
                if (annotations && annotations.length > 0) {
                    annotations.forEach(annotation => {
                        showComment(annotation.id);
                    });
                }
            });

            // Event: When annotation is clicked
            anno.on('clickAnnotation', (annotation, event) => {
                console.log('Annotation clicked:', annotation);
                // Show comment for clicked annotation
                showComment(annotation.id);
            });

            // Event: When mouse enters annotation - SHOW COMMENT
            anno.on('mouseEnterAnnotation', (annotation, event) => {
                console.log('Mouse entered annotation:', annotation);
                // Show comment for this annotation
                showComment(annotation.id);
            });

            // Event: When mouse leaves annotation - HIDE COMMENT (unless selected)
            anno.on('mouseLeaveAnnotation', (annotation, event) => {
                console.log('Mouse left annotation:', annotation);

                // Check if this annotation is currently selected
                const isSelected = window.selectedAnnotations &&
                                 window.selectedAnnotations.some(selected => selected.id === annotation.id);

                // Only hide comment if annotation is not selected
                if (!isSelected) {
                    hideComment(annotation.id);
                }
            });
        });
    </script>
</body>

</html>